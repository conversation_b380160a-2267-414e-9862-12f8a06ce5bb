package com.sinitek.sirm.nocode.form.support.datascope;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.common.utils.SQLUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.dto.CustomOrderItemDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchFieldDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.enumerate.LogicOperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import com.sinitek.sirm.nocode.form.support.condition.ConditionQuery;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedCaseInsensitiveMap;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 自定义数据权限过滤器
 *
 * <p>负责处理自定义数据权限的条件设置和查询优化</p>
 * <p>支持复杂的条件组合、排序和特殊字段类型处理</p>
 *
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class CustomFilterDataScope implements DataScopeConditionSetter, SmartInitializingSingleton {

    @Resource
    private List<ConditionQuery> conditionQueryList;

    private static final Map<String, ConditionQuery> conditionQueryMap = new HashMap<>();


    /**
     * 表单数据字段映射表
     */
    private static final Map<String, Class<?>> SYSTEM_COLUMN_MAP = new LinkedCaseInsensitiveMap<>();

    // ==================== 公共接口实现 ====================

    @Override
    public void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth,
                             String currentOrgId, Set<String> orgIds) {
        try {
            ZdFormDataScopeCustomConditionDTO customCondition = auth.getCustomDataScope();
            setCondition(queryWrapper, customCondition);
            log.debug("成功应用自定义数据权限条件，条件数量: {}",
                    customCondition.getConditions().size());
        } catch (Exception e) {
            log.error("应用自定义数据权限条件失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 核心业务方法 ====================

    /**
     * 设置查询条件
     *
     * @param queryWrapper    查询包装器
     * @param customCondition 自定义条件DTO
     */
    public static void setCondition(QueryWrapper<ZdPageFormData> queryWrapper,
                                    ZdFormDataScopeCustomConditionDTO customCondition) {
        if (Objects.isNull(customCondition) || CollectionUtils.isEmpty(customCondition.getConditions())) {
            log.warn("自定义条件为空，跳过条件设置");
            return;
        }

        LogicOperatorEnum logicOperator = Optional.ofNullable(customCondition.getLogicOperator())
                .orElse(LogicOperatorEnum.AND);
        List<ZdFormDataSearchFieldDTO> conditions = customCondition.getConditions();

        queryWrapper.and(queryBuilder ->
                applyConditions(queryBuilder, conditions, logicOperator));
    }

    /**
     * 应用查询条件列表
     *
     * @param queryBuilder  查询构建器
     * @param conditions    条件列表
     * @param logicOperator 逻辑操作符
     */
    private static void applyConditions(QueryWrapper<ZdPageFormData> queryBuilder,
                                        List<ZdFormDataSearchFieldDTO> conditions,
                                        LogicOperatorEnum logicOperator) {
        for (int i = 0; i < conditions.size(); i++) {
            ZdFormDataSearchFieldDTO condition = conditions.get(i);

            if (StringUtils.isBlank(condition.getKey())) {
                log.debug("跳过空键条件: {}", condition);
                continue;
            }

            try {
                applySingleCondition(queryBuilder, condition);

                // 添加逻辑操作符（除了最后一个条件）
                if (i < conditions.size() - 1 && LogicOperatorEnum.OR.equals(logicOperator)) {
                    queryBuilder.or();
                }
            } catch (Exception e) {
                log.error("应用条件失败，跳过该条件: {}, 错误: {}", condition.getKey(), e.getMessage(), e);
            }
        }
    }

    /**
     * 应用单个查询条件
     *
     * @param queryBuilder 查询构建器
     * @param condition    查询条件
     */
    private static void applySingleCondition(QueryWrapper<ZdPageFormData> queryBuilder,
                                             ZdFormDataSearchFieldDTO condition) {
        String key = condition.getKey();
        // 处理系统字段
        if (key.startsWith(ZdPgSqlConstant.FIELD_PREFIX)) {
            applySystemFieldCondition(queryBuilder, condition);
            return;
        }

        // 处理自定义字段
        applyCustomFieldCondition(queryBuilder, condition);
    }

    /**
     * 应用系统字段条件
     *
     * @param queryBuilder 查询构建器
     * @param condition    查询条件
     */
    private static void applySystemFieldCondition(QueryWrapper<ZdPageFormData> queryBuilder,
                                                  ZdFormDataSearchFieldDTO condition) {
        String key = condition.getKey();
        OperatorEnum operator = condition.getOperator();
        Object value = condition.getValue();
        String columnName = key.substring(ZdPgSqlConstant.FIELD_PREFIX.length());
        Class<?> fieldType = SYSTEM_COLUMN_MAP.get(columnName);

        if (Objects.isNull(fieldType)) {
            log.warn("未知的系统字段: {}", columnName);
            return;
        }

        // 对于LIKE操作，强制使用字符串类型
        if (isLikeOperation(operator)) {
            fieldType = String.class;
            columnName = ValueTypeEnum.STRING.addPgType(columnName);
        }

        operator.apply(fieldType, queryBuilder, columnName, value);
    }

    /**
     * 应用自定义字段条件
     *
     * @param queryBuilder 查询构建器
     * @param condition    查询条件
     */
    private static void applyCustomFieldCondition(QueryWrapper<ZdPageFormData> queryBuilder,
                                                  ZdFormDataSearchFieldDTO condition) {
        ConditionQuery conditionQuery = conditionQueryMap.get(SQLUtils.getDbType());
        conditionQuery.applyCustomFieldCondition(queryBuilder, condition);
    }


    /**
     * 应用排序条件
     *
     * @param queryWrapper 查询包装器
     * @param orderItems   排序项列表
     */
    public static void order(QueryWrapper<ZdPageFormData> queryWrapper, List<CustomOrderItemDTO> orderItems) {
        if (CollectionUtils.isEmpty(orderItems)) {
            log.debug("排序项列表为空，跳过排序设置");
            return;
        }

        orderItems.forEach(item -> {
            try {
                applyOrderItem(queryWrapper, item);
            } catch (Exception e) {
                log.error("应用排序项失败，跳过该项: {}, 错误: {}", item.getOrderName(), e.getMessage(), e);
            }
        });

        log.debug("成功应用排序条件，排序项数量: {}", orderItems.size());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 解析自定义数据权限配置
     *
     * @param customDataScope 自定义数据权限JSON字符串
     * @return 解析后的条件DTO
     */
    private ZdFormDataScopeCustomConditionDTO parseCustomDataScope(String customDataScope) {
        if (StringUtils.isBlank(customDataScope)) {
            throw new BussinessException(true, "自定义数据权限条件不能为空");
        }

        try {
            ZdFormDataScopeCustomConditionDTO result = JsonUtil.toJavaObject(
                    customDataScope, ZdFormDataScopeCustomConditionDTO.class);

            if (Objects.isNull(result)) {
                throw new BussinessException(true, "自定义数据权限条件格式错误");
            }

            return result;
        } catch (Exception e) {
            log.error("解析自定义数据权限条件失败: {}", customDataScope, e);
            throw new BussinessException(true, "自定义数据权限条件解析失败: " + e.getMessage());
        }
    }

    /**
     * 应用单个排序项
     *
     * @param queryWrapper 查询包装器
     * @param orderItem    排序项
     */
    private static void applyOrderItem(QueryWrapper<ZdPageFormData> queryWrapper, CustomOrderItemDTO orderItem) {
        String columnName = buildColumnName(orderItem.getOrderName(), orderItem.getValueType());
        boolean isAscending = PageDataParam.ASC.equalsIgnoreCase(orderItem.getOrderType());

        // 对于非字符串类型，需要进行类型转换
        ValueTypeEnum valueType = orderItem.getValueType();
        if (Objects.nonNull(valueType) && !ValueTypeEnum.STRING.equals(valueType)) {
            columnName = valueType.addPgType(columnName);
        }
        queryWrapper.orderBy(true, isAscending, columnName);
    }

    /**
     * 判断是否为LIKE操作
     *
     * @param operator 操作符
     * @return 是否为LIKE操作
     */
    private static boolean isLikeOperation(OperatorEnum operator) {
        return OperatorEnum.LIKE.equals(operator) || OperatorEnum.NOT_LIKE.equals(operator);
    }


    /**
     * 构建列名
     *
     * @param key       字段键
     * @param valueType 值类型
     * @return 构建后的列名
     */
    private static String buildColumnName(String key, ValueTypeEnum valueType) {
        String columnName = buildStringColumnName(key);
        return Optional.ofNullable(valueType)
                .map(type -> type.addPgType(columnName))
                .orElse(columnName);
    }

    /**
     * 构建字符串列名
     *
     * @param key 字段键
     * @return 字符串形式的列名
     */
    private static String buildStringColumnName(String key) {
        return String.format(ZdPgSqlConstant.FORM_DATA_KEY_TEXT, key);
    }


    // ==================== 静态初始化 ====================

    static {
        initializeSystemColumnMap();
    }

    /**
     * 初始化系统字段映射表
     */
    private static void initializeSystemColumnMap() {
        SYSTEM_COLUMN_MAP.put("id", Long.class);
        SYSTEM_COLUMN_MAP.put("status", Integer.class);
        SYSTEM_COLUMN_MAP.put("approve_status", Integer.class);
        SYSTEM_COLUMN_MAP.put("creator_id", String.class);
        SYSTEM_COLUMN_MAP.put("createtimestamp", LocalDate.class);
        SYSTEM_COLUMN_MAP.put("updatetimestamp", LocalDate.class);

        log.debug("系统字段映射表初始化完成，字段数量: {}", SYSTEM_COLUMN_MAP.size());
    }


    @Override
    public void afterSingletonsInstantiated() {
        if (CollectionUtils.isEmpty(conditionQueryList)) {
            return;
        }
        conditionQueryList.forEach(CustomFilterDataScope::put);
    }

    /**
     * 注册条件查询
     *
     * @param conditionQuery 条件查询
     */
    public static void put(ConditionQuery conditionQuery) {
        DbType dbType = conditionQuery.dbType();
        if (Objects.isNull(dbType)) {
            log.error("dbType不能为空：{}", conditionQuery.getClass().getName());
        }
        conditionQueryMap.put(conditionQuery.dbType().getDb(), conditionQuery);
    }
}
