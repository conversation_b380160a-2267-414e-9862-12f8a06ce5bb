package com.sinitek.sirm.nocode.form.support.datascope;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自定义部门数据权限
 *
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class CustomDepartmentDataScope implements DataScopeConditionSetter {
    @Resource
    private IOrgService orgService;

    @Override
    public void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth, String currentOrgId, Set<String> orgIds) {
        List<String> departmentIdList = auth.getDepartmentIdList();
        if (CollectionUtils.isEmpty(departmentIdList)) {
            throw new BussinessException(true, "部门不能为空");
        }
        setCondition(departmentIdList, orgIds);
    }

    void setCondition(List<String> departmentIdList, Set<String> orgIds) {
        EmployeeSearchDTO employeeSearchDTO = new EmployeeSearchDTO();
        // 设置在职
        employeeSearchDTO.setInservice("1");
        employeeSearchDTO.setSelectDeptIdList(departmentIdList);
        // 搜索符合条件的人员
        List<Employee> allEmployees = orgService.findAllEmployees(employeeSearchDTO);
        List<String> orgIdList = allEmployees.stream().map(Employee::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            orgIds.addAll(orgIdList);
        }
    }
}
