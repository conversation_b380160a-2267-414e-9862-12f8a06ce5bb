package com.sinitek.sirm.nocode.form.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.data.model.version.enumerate.VersionStatusEnum;
import com.sinitek.data.model.version.util.VersionUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormDAO;
import com.sinitek.sirm.nocode.form.dto.ZdComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormPageDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormTransDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormUpdateDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageForm;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormFieldMappingService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormHistoryService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 11:04:49
 * @description 针对表【zd_page_form(页面表单表)】的数据库操作Service实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZdPageFormServiceImpl extends BaseDAO<ZdPageFormMapper, ZdPageForm, ZdPageFormDAO>
        implements IZdPageFormService {

    private final IZdPageFormConfigService pageFormConfigService;
    private final IZdPageFormHistoryService pageFormHistoryService;
    private final IZdPageFormFieldMappingService pageFormFieldMappingService;
    private final ZdOrgUtil orgUtil;


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        if (CollectionUtils.isEmpty(pageCodeList)) {
            return;
        }
        LambdaQueryWrapper<ZdPageForm> queryWrapper = eqOrIn(ZdPageForm::getPageCode, pageCodeList)
                .select(ZdPageForm::getId, ZdPageForm::getCode);
        List<String> formCodeList = new ArrayList<>();
        List<Long> formIdList = new ArrayList<>();
        dao.list(queryWrapper).forEach(p -> {
            formCodeList.add(p.getCode());
            formIdList.add(p.getId());
        });
        if (CollectionUtils.isNotEmpty(formCodeList)) {
            SpringUtil.publishEvent(new AppDeleteEvent(AppConstant.DE_FORM_CONFIG, formCodeList, formIdList));
        }
        dao.remove(queryWrapper);
        log.info("删除表单成功！");
    }

    /**
     * 创建页面后创建表单
     *
     * @param pageCreateEvent 创建页面事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(condition = "T(com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum).isForm(#pageCreateEvent.source.pageType)")
    public void createForm(PageCreateEvent pageCreateEvent) {
        ZdPageDTO source = pageCreateEvent.getSource();
        String code = source.getCode();
        ZdPageForm zdPageForm = new ZdPageForm();
        zdPageForm.setPageCode(code);
        zdPageForm.setCode(code);
        dao.saveVersion(zdPageForm);
        // 这里需要创建一个表单配置，用来存储表单的配置信息，比如:表的名称
        pageFormConfigService.createFormConfig(code, source.getPageType(), source.getName());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdate(ZdPageFormUpdateDTO pageFormDTO) {
        // 只有未发布的表单才会新增或者修改
        // 同一时间，未发布的只有一条
        ZdPageForm zdPageForm = getNoPublish(pageFormDTO.getCode());
        if (Objects.isNull(zdPageForm)) {
            // 无法找到未发布的表单
            // 找到最新有效的数据
            zdPageForm = getNewAndPublished(pageFormDTO.getCode());
            if (Objects.isNull(zdPageForm)) {
                // 无法找到最新有效的数据
                throw new BussinessException("3000020");
            }
            // 表单数据恢复
            String pageData = zdPageForm.getPageData();
            // 执行更新操作
            zdPageForm = (ZdPageForm) updateVersion(zdPageForm.getId());
            zdPageForm.setPageData(pageData);
        }
        Long id = zdPageForm.getId();
        String newPageData = pageFormDTO.getPageData();
        String pageData = zdPageForm.getPageData();
        // 数据不一样
        if (!Objects.equals(newPageData, pageData)) {
            // 之前的不为空的话，需要保存历史
            if (StringUtils.isNotBlank(pageData)) {
                ZdPageFormHistory zdPageFormHistory = new ZdPageFormHistory();
                zdPageFormHistory.setPageFormId(id);
                zdPageFormHistory.setPageData(pageData);
                zdPageFormHistory.setUpdateVersion(getMaxUpdateVersion(id, pageFormDTO.getCode()));
                pageFormHistoryService.create(zdPageFormHistory);
            }
            // 更改表单数据
            LambdaUpdateWrapper<ZdPageForm> lu = lu().set(ZdPageForm::getPageData, newPageData)
                    .eq(ZdPageForm::getId, id);
            return dao.update(lu);
        }
        return true;
    }

    @Override
    public ZdPageFormDTO view(String code) {

        // 获取最新的一条数据
        LambdaUpdateWrapper<ZdPageForm> query = lu().eq(ZdPageForm::getCode, code)
                .eq(ZdPageForm::getThreadLatestFlag, 1);
        ZdPageForm zdPageForm = dao.getOne(query);
        if (Objects.isNull(zdPageForm)) {
            return null;
        }
        Integer publishStatus = zdPageForm.getPublishStatus();
        // 只有未发布和已发布的，才可以预览
        if (VersionUtil.isPublishAvailable(publishStatus) || VersionUtil.isUpdateVersionAvailable(publishStatus)) {
            return zdPageForm;
        }
        return null;
    }

    @Override
    public ZdPageFormDTO getPublishedForm(String code) {
        // 获取发布的表单
        return getByPublishStatus(code, VersionStatusEnum.PUBLISHED);
    }

    /**
     * 获取表单页面数据
     *
     * @param code 表单编码
     * @return 组件数据列表，若未找到则返回null
     */
    @Override
    public ZdPageFormTransDTO getFormPageData(String code) {
        ZdPageFormTransDTO result = new ZdPageFormTransDTO();
        // 构建查询条件：查找已发布的表单
        LambdaQueryWrapper<ZdPageForm> query = eqOrIn(ZdPageForm::getCode, code)
                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.PUBLISHED.getValue())
                .select(ZdPageForm::getPageData);

        // 执行查询获取已发布表单数据
        String pageData = stringValue(LamWrapper.ins(query));

        // 如果没有已发布数据，尝试获取最新未发布表单
        if (StringUtils.isBlank(pageData)) {
            query = eqOrIn(ZdPageForm::getCode, code)
                    .eq(ZdPageForm::getThreadLatestFlag, 1)
                    .select(ZdPageForm::getPageData);
            pageData = stringValue(LamWrapper.ins(query));
        }

        // 如果仍然没有数据，直接返回null
        if (StringUtils.isBlank(pageData)) {
            return result;
        }

        // 将JSON字符串转换为表单数据对象
        ZdFormPageDataDTO zdFormPageDataDTO = JsonUtil.toJavaObject(pageData, ZdFormPageDataDTO.class);

        // 定义需要过滤的组件类型常量
        String form = PageDataComponentTypeEnum.ZD_FORM.getValue();
        String zdChildForm = PageDataComponentTypeEnum.ZD_CHILD_FORM.getValue();
        String page = PageDataComponentTypeEnum.ZD_PAGE.getValue();

        // 初始化结果列表
        List<ZdComponentDTO> list = new ArrayList<>();
        // 遍历组件树，过滤掉特定类型的组件并构建结果
        zdFormPageDataDTO.walk((a, b) -> {
            if (Objects.nonNull(a)) {
                String componentName = b.getComponentName();
                String parentComponentName = a.getComponentName();
                if (!(Objects.equals(form, componentName) || Objects.equals(zdChildForm, componentName) || Objects.equals(page, componentName))) {
                    ZdComponentDTO zdComponentDTO = b.makeComponentDTO(parentComponentName);
                    String label = zdComponentDTO.getLabel();
                    if (StringUtils.isNotBlank(label)) {
                        list.add(zdComponentDTO);
                    }
                }
            }
        });
        result.setPageData(pageData);
        result.setPageDataDTOList(list);
        return result;
    }


    /**
     * 根据id获取表单编码
     *
     * @param id 表单id
     * @return 表单编码
     */
    private String getCodeById(Long id) {
        LamWrapper<ZdPageForm> query = LamWrapper.eqOrIn(ZdPageForm::getId, id).select(ZdPageForm::getCode);
        return stringValue(query);
    }


    /**
     * 最新有效记录
     *
     * @param formCode 表单编码
     * @return 最新的表单
     */
    private ZdPageForm getNewAndPublished(String formCode) {
        LambdaQueryWrapper<ZdPageForm> query = eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getLatestFlag, 1);
        return dao.getOne(query);
    }

    /**
     * 最新记录,不管是不是有效的
     *
     * @param formCode 表单编码
     * @return 最新的表单
     */
    private ZdPageForm getNewest(String formCode) {
        LambdaQueryWrapper<ZdPageForm> query = eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getThreadLatestFlag, 1);
        return dao.getOne(query);
    }

    /**
     * 获取未发布的表单
     *
     * @param formCode 表单编码
     * @return 未发布的表单
     */
    private ZdPageForm getNoPublish(String formCode) {
        return getByPublishStatus(formCode, VersionStatusEnum.UN_PUBLISHED);
    }

    /**
     * 获取指定状态的表单
     *
     * @param formCode          表单编码
     * @param versionStatusEnum 表单状态
     * @return 指定状态的表单
     */
    private ZdPageForm getByPublishStatus(String formCode, VersionStatusEnum versionStatusEnum) {
        LambdaQueryWrapper<ZdPageForm> queryWrapper = eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getPublishStatus, versionStatusEnum.getValue());
        return dao.getOne(queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZdPageFormDTO updateVersion(Long id) {
        // 不能无限更新
        ZdPageForm noPublish = getNoPublish(getCodeById(id));
        if (Objects.nonNull(noPublish)) {
            // 已经存在未发布的表单
            throw new BussinessException("3000019");
        }
        return dao.updateVersion(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(String code) {
        // 只有未发布的表单才可以发布
        ZdPageForm zdPageForm = getNoPublish(code);
        // 没有找到未发布的表单
        if (Objects.isNull(zdPageForm)) {
            // 无法找到未发布的表单
            throw new BussinessException("3000020");
        }
        dao.publish(zdPageForm.getId());
        // 发布完后，做字段映射
        pageFormFieldMappingService.saveOrUpdate(zdPageForm.getCode(), zdPageForm.getPageData());

    }

    @Override
    public void effectVersion(ZdPageFormDTO zdPageFormDTO) {
        ZdPageForm zdPageForm = new ZdPageForm();
        BeanUtils.copyProperties(zdPageFormDTO, zdPageForm);
        dao.effectVersion(zdPageForm);
    }

    @Override
    public ZdPageFormDTO applyHistoryVersion(Long id) {
        return dao.applyHistoryVersion(id);
    }

    @Override
    public List<ZdPageFormDTO> versionHistory(Long id) {
        List<ZdPageFormDTO> versions = ConvertUtil.list(dao.findVersions(id));
        // 设置组织名称
        orgUtil.setOrgName(versions, ZdPageFormDTO::getUpdaterId, ZdPageFormDTO::setUpdater);
        return versions;
    }


    @Override
    public List<ZdPageFormHistoryDTO> updateHistory(String formCode) {
        List<Long> formIdList = findFormIdListByCode(formCode);
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return pageFormHistoryService.findByFormIdList(formIdList);
    }

    @Override
    public String getAppCodeByFormId(Long formId) {
        return getBaseMapper().getAppCodeByFormId(formId);
    }

    @Override
    public boolean codeBelongsToAppCode(String formCode, String appCode) {
        return BooleanUtils.isTrue(getBaseMapper().codeBelongsToAppCode(formCode, appCode));
    }

    @Override
    public boolean exists(String formCode) {
        return exists(ZdPageForm::getCode, formCode);
    }

    @Override
    public boolean isPublished(String formCode) {
        LamWrapper<ZdPageForm> query = LamWrapper.eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.PUBLISHED.getValue());
        return exists(query);
    }

    @Override
    public List<Long> findFormIdListByCode(String formCode) {
        LambdaQueryWrapper<ZdPageForm> queryWrapper = eqOrIn(ZdPageForm::getCode, formCode)
                .select(ZdPageForm::getId);
        return list(queryWrapper).stream().map(ZdPageForm::getId).collect(Collectors.toList());

    }

    /**
     * 获取表单最大更新版本
     *
     * @param formId   表单id
     * @param formCode 表单编码
     * @return 表单最大更新版本
     */
    private Integer getMaxUpdateVersion(Long formId, String formCode) {
        List<Long> formIdList = findFormIdListByCode(formCode);
        Long i = pageFormHistoryService.countByFormId(formIdList);
        return i.intValue();
    }
}




