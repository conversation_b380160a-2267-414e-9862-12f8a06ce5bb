package com.sinitek.sirm.nocode.app.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.sinitek.cloud.common.utils.JWTUtils;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dao.ZdAppDAO;
import com.sinitek.sirm.nocode.app.dto.ZdAppAccessTokenRequestDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppBaseInfoDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppCustomUrlDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppGetAccessTokenResponseBodyDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSaveDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSecretDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppUpdateNameDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import com.sinitek.sirm.nocode.app.entity.ZdAppManager;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.app.mapper.ZdAppMapper;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.app.support.IZdAppSettingCustomizer;
import com.sinitek.sirm.nocode.appmanager.service.impl.ZdAppManagerServiceImpl;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import com.sinitek.sirm.nocode.common.utils.CodeCreateUtil;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-11 09:32:06
 * @description 针对表【zd_app(应用表)】的数据库操作Service实现
 */
@Service
@Slf4j
public class ZdAppServiceImpl extends BaseDAO<ZdAppMapper, ZdApp, ZdAppDAO>
        implements IZdAppService {
    private static final int randomLength = 32;
    /**
     * 默认两个小时的过期时间
     */
    @Value("${nocode.app.token.expire.time:7200000}")
    private Long appTokenExpireTime;
    @Resource
    private ZdAppManagerServiceImpl appManagerService;

    @Resource
    private List<IZdAppSettingCustomizer> zdAppSettingCustomizers;


    @Override
    public ZdAppDTO getById(Long id) {
        return dao.getById(id);
    }

    @Override
    public String getCodeById(Long id) {
        return stringValue(LamWrapper.eqOrIn(ZdApp::getId, id).select(ZdApp::getCode));
    }

    @Override
    public IPage<ZdAppDTO> search(ZdAppSearchParamDTO param) {
        String name = param.getName();
        if (StringUtils.isNotBlank(name) && (name.length() == 19) && NumberUtil.isNumber(name)) {
            param.setId(NumberTool.safeToLong(name, 0L));
            param.setName(null);
        }
        return dao.getBaseMapper().search(param.buildPage(), param);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(ZdAppSaveDTO saveDTO) {
        String currentOrgId = saveDTO.getCurrentOrgId();
        if (StringUtils.isBlank(currentOrgId)) {
            throw new BussinessException("3000006");
        }
        ZdApp zdApp = new ZdApp();
        zdApp.setName(saveDTO.getName());
        zdApp.setStatus(StatusEnum.ENABLE);
        // 设置应用编码
        zdApp.setCode(CodeCreateUtil.createCode(AppConstant.APPCODE_PREFIX));
        // 设置密钥
        zdApp.setAppSecret(RandomStringUtils.random(randomLength, true, true) + UUID.fastUUID().toString(true));
        dao.save(zdApp);
        // 新增一个应用的时候需要新增一个应用管理员
        ZdAppManager zdAppManager = new ZdAppManager();
        zdAppManager.setAppCode(zdApp.getCode());
        zdAppManager.setOrgId(saveDTO.getCurrentOrgId());
        appManagerService.save(zdAppManager);
        return zdApp.getCode();
    }

    @Override
    public Boolean updateName(ZdAppUpdateNameDTO saveDTO) {
        String code = saveDTO.getCode();
        LambdaUpdateWrapper<ZdApp> updateWrapper = Wrappers.<ZdApp>lambdaUpdate()
                .set(StringUtils.isNotBlank(code), ZdApp::getName, saveDTO.getName())
                .eq(ZdApp::getCode, code);
        return dao.update(updateWrapper);
    }

    @Override
    public Boolean update(ZdAppDTO zdAppDTO) {
        // 修改时id 一定存在
        Long id = zdAppDTO.getId();
        ZdApp orgApp = null;
        if (IdUtil.isDataId(id)) {
            orgApp = idCheck(id);
        }
        if (Objects.nonNull(orgApp)) {
            String code = zdAppDTO.getCode();
            orgApp = (ZdApp) getByCode(code);
        }
        if (Objects.isNull(orgApp)) {
            throw new BussinessException("3000007");
        }
        CopyUtil.copyEntityProperties(zdAppDTO, orgApp, "appSecret", "url", "code");
        return dao.updateById(orgApp);
    }

    @Override
    public ZdAppDTO getByCode(String appCode) {
        return dao.getOne(codeQuery(appCode));
    }

    @Override
    public boolean exists(String appCode) {
        return exists(ZdApp::getCode, appCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByCode(String appCode) {
        // 发出应用删除事件
        AppDeleteEvent appDeleteEvent = new AppDeleteEvent(AppConstant.DE_PAGE, appCode);
        SpringUtil.publishEvent(appDeleteEvent);
        boolean remove = dao.remove(codeQuery(appCode));
        log.info("删除应用：{}！", appCode);
        return remove;
    }

    @Override
    public Boolean updateStatus(String appCode) {
        return SqlHelper.retBool(getBaseMapper().updateStatus(appCode));
    }


    @Override
    public ZdAppSecretDTO viewSecret(String appCode) {
        LambdaQueryWrapper<ZdApp> queryWrapper = codeQuery(appCode).select(ZdApp::getAppSecret);
        ZdApp one = dao.getOne(queryWrapper);
        if (one != null) {
            ZdAppSecretDTO zdAppSecretDTO = new ZdAppSecretDTO();
            zdAppSecretDTO.setAppKey(appCode);
            zdAppSecretDTO.setAppSecret(one.getAppSecret());
            return zdAppSecretDTO;
        }
        return null;
    }

    @Override
    public ZdAppSettingDTO setting(String appCode) {
        ZdAppSettingDTO zdAppSettingDTO = new ZdAppSettingDTO();
        zdAppSettingDTO.setApp(getByCode(appCode));
        zdAppSettingCustomizers.forEach(c -> c.customize(zdAppSettingDTO, appCode));
        ZdApp zdApp = dao.getOne(codeQuery(appCode).select(ZdApp::getId, ZdApp::getName, ZdApp::getUrl));
        ZdAppCustomUrlDTO appCustomUrlDTO = new ZdAppCustomUrlDTO();
        if (Objects.nonNull(zdApp)) {
            appCustomUrlDTO.setId(zdApp.getId());
            appCustomUrlDTO.setUrl(zdApp.getUrl());
            appCustomUrlDTO.setName(zdApp.getName());
        }
        zdAppSettingDTO.setPageUrl(appCustomUrlDTO);
        return zdAppSettingDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePageCustomUrl(List<ZdAppCustomUrlDTO> appCustomUrlDTOList, String orgId) {
        check(appCustomUrlDTOList);
        appCustomUrlDTOList.forEach(appCustomUrlDTO -> dao.update(lu().set(ZdApp::getUrl, appCustomUrlDTO.getUrl()).eq(ZdApp::getId, appCustomUrlDTO.getId())));
    }

    @Override
    public ZdAppGetAccessTokenResponseBodyDTO accessToken(ZdAppAccessTokenRequestDTO appAccessTokenRequestDTO) {
        String code = appAccessTokenRequestDTO.getCode();
        String appSecret = appAccessTokenRequestDTO.getAppSecret();
        LambdaQueryWrapper<ZdApp> eq = codeQuery(code).eq(ZdApp::getAppSecret, appSecret);
        int count = dao.count(eq);
        if (count > 0) {
            String accessToken = JWTUtils.commonCreateJwt(code, appTokenExpireTime);
            ZdAppGetAccessTokenResponseBodyDTO zdAppGetAccessTokenResponseBodyDTO = new ZdAppGetAccessTokenResponseBodyDTO();
            zdAppGetAccessTokenResponseBodyDTO.setAccessToken(accessToken);
            zdAppGetAccessTokenResponseBodyDTO.setExpireIn(appTokenExpireTime);
            return zdAppGetAccessTokenResponseBodyDTO;
        }
        return null;
    }

    @Override
    public boolean isEnable(String appCode) {
        LambdaQueryWrapper<ZdApp> select = codeQuery(appCode).select(ZdApp::getStatus);
        ZdApp one = dao.getOne(select);
        if (Objects.nonNull(one)) {
            return StatusEnum.ENABLE.equals(one.getStatus());
        }
        return false;
    }

    @Override
    public ZdAppBaseInfoDTO getBaseInfo(String appCode, String currentOrgId) {
        ZdAppBaseInfoDTO baseInfoDTO = new ZdAppBaseInfoDTO();
        ZdAppDTO one = getByCode(appCode);
        if (Objects.nonNull(one)) {
            baseInfoDTO.setName(one.getName());
            baseInfoDTO.setStatus(one.getStatus());
            baseInfoDTO.setBackground(one.getBackground());
            baseInfoDTO.setThemeColor(one.getThemeColor());
            baseInfoDTO.setAdminFlag(appManagerService.hasAuth(appCode, currentOrgId));
        }
        return baseInfoDTO;
    }

    /**
     * 根据编码查询
     *
     * @param code 应用编码
     * @return query
     */
    private LambdaQueryWrapper<ZdApp> codeQuery(String code) {
        return eqOrIn(ZdApp::getCode, code);
    }

    /**
     * 校验地址是否存在
     *
     * @param appCustomUrlDTOList 应用自定义URL列表
     */
    private void check(List<ZdAppCustomUrlDTO> appCustomUrlDTOList) {
        appCustomUrlDTOList.forEach(appCustomUrlDTO -> {
            Long id = appCustomUrlDTO.getId();
            String url = appCustomUrlDTO.getUrl();
            LamWrapper<ZdApp> queryWrapper = LamWrapper.eqOrIn(ZdApp::getUrl, url)
                    .select(ZdApp::getId);
            Long oneId = longValue(queryWrapper);
            if (Objects.nonNull(oneId) && !Objects.equals(id, oneId)) {
                throw new BussinessException("3000018", url);
            }
        });
    }
}




