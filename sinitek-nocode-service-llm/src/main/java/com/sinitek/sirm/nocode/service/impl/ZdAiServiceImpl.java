package com.sinitek.sirm.nocode.service.impl;

import cn.hutool.json.JSONUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.constant.LlmGeneratorConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentGenFunctionDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.service.IZdAiService;
import com.sinitek.sirm.nocode.support.util.SqlQueryUtil;
import com.sinitek.sirm.nocode.util.LlmTextUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI服务实现类
 *
 * <p>负责处理各种AI相关的业务逻辑，包括智能体功能生成、
 * 图表生成、统计提示等核心AI服务功能。</p>
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
@Service
@Slf4j
public class ZdAiServiceImpl implements IZdAiService {

    @Autowired
    private LlmServiceImpl llmService;

    @Autowired
    private LlmWithImageServiceImpl llmWithImageService;

    @Autowired
    private SqlQueryUtil sqlQueryUtil;

    @Autowired
    private IZdPageFormService pageFormService;

    @Override
    public LlmAgentCompletionsResponseDTO agentGenerateFunction(LlmAgentGenFunctionDTO request, List<MultipartFile> images) {
        LlmAgentCompletionsRequestDTO agentCompletionsRequestDTO = buildAgentRequest(request, images);
        return executeAgentFunction(agentCompletionsRequestDTO);
    }

    @Override
    public LlmChatGenDiagramResponseDTO agentGenerateDiagram(LlmChatGenDiagramDTO request, List<MultipartFile> images) {
        log.info("开始生成图表，表单代码: {}, 需求: {}", request.getFormCode(), request.getPrompt());
        return executeGenerateDiagram(request, images, false);
    }

    @Override
    public List<String> generateStatisticPrompt(String formCode) {
        // 获取表单配置
        ZdPageFormDTO formConfig = pageFormService.view(formCode);

        // 构建AI提示词，分析表单结构并生成统计建议
        String prompt = JSONUtil.toJsonStr(formConfig.getPageData()) + "\n\n上面是一个表格数据配置，请你分析出用户可能想做的数据统计，用自然语言表达,给出5个用户可能要的例子，返回给我一个JSON数组,比如" +
            "\n\n```json\n[\"统计一下数据提交数量，按天统计\",\"统计一下数据提交数量，按月统计\"]\n```";

        LlmChatCompletionsRequestDTO requestDTO = new LlmChatCompletionsRequestDTO();
        requestDTO.setPrompt(prompt);
        requestDTO.setFastLlm(true);

        LlmChatCompletionsResponseDTO responseDTO = llmService.chatCompletions(requestDTO);
        String text = LlmTextUtil.formatToJson(responseDTO.getText());
        return JSONUtil.toList(text, String.class);
    }

    /**
     * 构建智能体请求对象
     *
     * <p>根据用户输入和上传的图片构建智能体调用请求，
     * 包括图片处理、参数设置等预处理逻辑。</p>
     *
     * @param request 用户请求参数
     * @param images 上传的图片列表
     * @return 智能体请求对象
     */
    private LlmAgentCompletionsRequestDTO buildAgentRequest(LlmAgentGenFunctionDTO request, List<MultipartFile> images) {
        LlmAgentCompletionsRequestDTO agentCompletionsRequestDTO = new LlmAgentCompletionsRequestDTO();
        agentCompletionsRequestDTO.setPrompt(request.getPrompt());

        Map<String, Object> params = new HashMap<>();
        params.put("schema", request.getSchema());

        // 处理图片描述
        if (images != null && !images.isEmpty()) {
            String imageDesc = processImages(images);
            agentCompletionsRequestDTO.setImageDesc(imageDesc);
        }

        agentCompletionsRequestDTO.setParams(params);
        agentCompletionsRequestDTO.setAgentId(LlmGeneratorConstant.AGENT_GEN_SCHEMA);
        agentCompletionsRequestDTO.setSessionId(request.getSessionId());

        return agentCompletionsRequestDTO;
    }

    /**
     * 处理上传的图片文件
     *
     * <p>将MultipartFile转换为临时文件，调用图像识别服务获取图片描述，
     * 处理完成后自动清理临时文件。</p>
     *
     * @param images 上传的图片文件列表
     * @return 图片描述文本
     * @throws RuntimeException 当文件处理失败时抛出
     */
    private String processImages(List<MultipartFile> images) {
        try {
            // 将MultipartFile转换为File列表
            List<File> imageFiles = new ArrayList<>();
            for (MultipartFile multipartFile : images) {
                String originalFilename = multipartFile.getOriginalFilename();
                String suffix = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".tmp";
                File tempFile = File.createTempFile("upload_", suffix);
                Files.copy(multipartFile.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                imageFiles.add(tempFile);
            }

            // 获取图片描述
            String imageDesc = llmWithImageService.imageDesc(imageFiles);

            // 清理临时文件
            imageFiles.forEach(file -> {
                try {
                    Files.deleteIfExists(file.toPath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", file.getAbsolutePath(), e);
                }
            });

            return imageDesc;
        } catch (IOException e) {
            log.error("处理上传文件失败", e);
            throw new RuntimeException("处理上传文件失败", e);
        }
    }

    /**
     * 执行智能体功能调用
     *
     * <p>调用底层LLM服务执行智能体任务，
     * 并对返回结果进行格式化处理。</p>
     *
     * @param request 智能体请求对象
     * @return 智能体响应结果
     * @throws RuntimeException 当智能体调用失败时抛出
     */
    private LlmAgentCompletionsResponseDTO executeAgentFunction(LlmAgentCompletionsRequestDTO request) {
        try {
            LlmAgentCompletionsResponseDTO responseDTO = llmService.agentCompletions(request);

            // 对响应文本进行二次处理
            String text = processAgentResponseText(responseDTO);
            responseDTO.setText(LlmTextUtil.formatToJS(text));

            return responseDTO;
        } catch (Exception e) {
            log.error("智能代理功能调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("智能代理功能调用失败", e);
        }
    }

    /**
     * 处理智能体响应文本
     *
     * <p>根据智能体完成状态，解析和格式化响应文本，
     * 提取有效的代码内容。</p>
     *
     * @param responseDTO 智能体原始响应
     * @return 处理后的文本内容
     */
    private String processAgentResponseText(LlmAgentCompletionsResponseDTO responseDTO) {
        String text;
        if (responseDTO.getFinishFlag() == LlmGeneratorConstant.FINISH_FLAG_COMPLETE) {
            text = responseDTO.getText();
            if (!text.startsWith("{") || !text.endsWith("}")) {
                text = LlmGeneratorConstant.END_FUNCTION;
            } else {
                Map<String, Object> dataMap = JsonUtil.toMap(text);
                text = (String) dataMap.get(LlmGeneratorConstant.ACTION_INPUT);
            }
        } else {
            text = responseDTO.getText();
        }
        return text;
    }

    /**
     * 执行图表生成主流程
     *
     * <p>完整的图表生成流程：SQL生成 → 数据查询 → 图表代码生成。
     * 支持SQL执行失败时的自动重试机制。</p>
     *
     * @param request 图表生成请求
     * @param isRetry 是否为重试执行
     * @return 图表生成响应
     * @throws RuntimeException 当生成过程失败时抛出
     */
    private LlmChatGenDiagramResponseDTO executeGenerateDiagram(LlmChatGenDiagramDTO request, List<MultipartFile> images, boolean isRetry) {
        // 1. 根据用户需求和表单配置生成SQL
        LlmChatSqlDTO sqlRequest = buildSqlRequest(request, images);
        String generatedSql;

        // 如果有图片，使用支持图像的LLM服务，否则使用普通的LLM服务
        if (images != null && !images.isEmpty()) {
            generatedSql = llmWithImageService.formSqlGenerate(sqlRequest);
            log.debug("使用图像LLM服务生成的SQL语句: {}", generatedSql);
        } else {
            generatedSql = llmService.formSqlGenerate(sqlRequest);
            log.debug("使用普通LLM服务生成的SQL语句: {}", generatedSql);
        }

        List<Map<String, Object>> queryResult = null;
        try {
            // 2. 执行SQL查询获取数据
            queryResult = sqlQueryUtil.executeQuery(generatedSql);
            log.debug("查询结果记录数: {}", queryResult.size());
        } catch (Exception sqlE) {
            // SQL执行失败时进行重试
            if (!isRetry) {
                log.warn("SQL执行出现异常，准备重试。错误信息: {}", sqlE.getMessage());
                return retryGenerateDiagram(request, generatedSql, sqlE);
            } else {
                log.error("重试后仍然出现PostgreSQL异常，表单代码: {}, 错误: {}",
                    request.getFormCode(), sqlE.getMessage(), sqlE);
                throw new RuntimeException("SQL执行失败，重试后仍然出现错误: " + sqlE.getMessage(), sqlE);
            }
        }

        // 3. 基于查询数据生成图表代码
        String diagramCode = generateDiagramFromData(request.getPrompt(), queryResult);

        // 4. 构建并返回响应
        LlmChatGenDiagramResponseDTO response = buildDiagramResponse(request.getSessionId(), diagramCode);

        log.info("图表生成完成，会话ID: {}", request.getSessionId());
        return response;
    }

    /**
     * 重试图表生成
     *
     * <p>当首次SQL执行失败时，将错误信息反馈给AI，
     * 让AI根据错误信息调整SQL并重新生成图表。</p>
     *
     * @param request 原始请求
     * @param generatedSql 失败的SQL语句
     * @param sqlE SQL执行异常
     * @return 重试后的图表生成响应
     */
    private LlmChatGenDiagramResponseDTO retryGenerateDiagram(LlmChatGenDiagramDTO request, String generatedSql, Exception sqlE) {
        // 构建包含错误信息的增强提示词
        String originalPrompt = request.getPrompt();
        String errorMessage = sqlE.getMessage();
        String enhancedPrompt = originalPrompt + "\n\n上次执行的SQL：" + generatedSql +
            "\n\n上次SQL执行出现错误：" + errorMessage +
            "\n请根据错误信息调整SQL语句。";

        // 创建重试请求
        LlmChatGenDiagramDTO retryRequest = new LlmChatGenDiagramDTO();
        retryRequest.setPrompt(enhancedPrompt);
        retryRequest.setFormCode(request.getFormCode());
        retryRequest.setSessionId(request.getSessionId());

        log.info("开始重试图表生成，增强后的需求: {}", enhancedPrompt);
        return executeGenerateDiagram(retryRequest, null,true);
    }

    /**
     * 构建SQL生成请求对象
     *
     * @param request 原始图表生成请求
     * @return SQL生成请求对象
     */
    private LlmChatSqlDTO buildSqlRequest(LlmChatGenDiagramDTO request,List<MultipartFile> images) {
        LlmChatSqlDTO sqlRequest = new LlmChatSqlDTO();
        sqlRequest.setPrompt(request.getPrompt());
        sqlRequest.setFormCode(request.getFormCode());
        sqlRequest.setSessionId(request.getSessionId());
        sqlRequest.setImages(images);
        return sqlRequest;
    }

    /**
     * 基于查询数据生成图表代码
     *
     * <p>将用户需求和查询得到的数据传递给AI，
     * 让AI生成对应的Mermaid格式图表代码。</p>
     *
     * @param userPrompt 用户需求描述
     * @param data 查询得到的数据
     * @return Mermaid格式的图表代码
     */
    private String generateDiagramFromData(String userPrompt, List<Map<String, Object>> data) {
        // 预处理数据，确保数据格式适合图表生成
        List<Map<String, Object>> processedData = preprocessDataForDiagram(data);

        // 构建图表生成提示词
        String diagramPrompt = String.format(
            "需求：%s \n\n数据：%s \n\n请根据上面的需求和数据生成mermaid代码。",
            userPrompt,
            JSONUtil.toJsonStr(processedData)
        );

        String diagramText = llmService.mermaidGenerate(diagramPrompt);

        // 格式化为标准的Mermaid代码块
        return "```mermaid\n" + LlmTextUtil.formatToMermaid(diagramText) + "\n```";
    }

    /**
     * 构建图表生成响应对象
     *
     * @param sessionId 会话ID
     * @param diagramCode 图表代码
     * @return 图表生成响应对象
     */
    private LlmChatGenDiagramResponseDTO buildDiagramResponse(String sessionId, String diagramCode) {
        LlmChatGenDiagramResponseDTO response = new LlmChatGenDiagramResponseDTO();
        response.setSessionId(sessionId);
        response.setText(diagramCode);
        return response;
    }

    /**
     * 预处理数据
     *
     * <p>对查询结果进行预处理，主要处理日期时间字段，
     * 将时间戳转换为可读的日期格式，便于图表展示。</p>
     *
     * @param data 原始查询数据
     * @return 处理后的数据
     */
    private List<Map<String, Object>> preprocessDataForDiagram(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        List<Map<String, Object>> processedData = new ArrayList<>();

        for (Map<String, Object> row : data) {
            Map<String, Object> processedRow = new HashMap<>();

            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 处理日期时间字段，确保格式统一
                Object processedValue = processDateTimeValue(value);
                processedRow.put(key, processedValue);
            }

            processedData.add(processedRow);
        }

        return processedData;
    }

    /**
     * 处理日期时间值
     *
     * <p>将Date对象转换为标准的日期时间字符串格式，
     * 其他类型值保持原样。</p>
     *
     * @param value 字段值
     * @return 处理后的值
     */
    private Object processDateTimeValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Date) {
            value = formatDate((Date) value);
        }

        return value;
    }

    /**
     * 格式化Date对象为字符串
     *
     * @param date Date对象
     * @return 格式化后的日期字符串 (yyyy-MM-dd HH:mm:ss)
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}
