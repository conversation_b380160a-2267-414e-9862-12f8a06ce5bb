<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.form.mapper.ZdPageFormHistoryMapper">
    <select id="exists" resultType="java.lang.Boolean">
        select 1
        from ${tableName}
        where id = #{id}
    </select>

    <!-- 根据表单ID查询历史记录 -->
    <select id="findByFormIdList" resultType="com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO">
        SELECT
        id,
        page_form_id,
        creator_id,
        update_version,
        createtimestamp,
        (select publish_status from zd_page_form where id = page_form_id) as publish_status
        FROM zd_page_form_history
        <where>
            <if test="formIdList != null and formIdList.size() > 0">
                page_form_id IN
                <foreach item="item" collection="formIdList" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by createtimestamp desc
    </select>
</mapper>