package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form_history")
@Data
@JsonIgnoreProperties({"entityNameValue", "version"})
public class ZdPageFormHistory extends ZdPageFormHistoryDTO {

}
