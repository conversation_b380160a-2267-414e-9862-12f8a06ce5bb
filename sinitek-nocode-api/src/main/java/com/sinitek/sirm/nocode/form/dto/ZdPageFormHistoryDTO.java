package com.sinitek.sirm.nocode.form.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.data.model.version.enumerate.VersionStatusEnum;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "历史表单DTO")
public class ZdPageFormHistoryDTO extends BaseAuditEntity {
    /**
     * 表单配置
     */
    @JsonSerialize(using = Base64Encode.class)
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "表单配置")
    private String pageData;

    /**
     * 页面表单id
     */
    @ApiModelProperty(value = "页面表单id")
    private Long pageFormId;


    @ApiModelProperty("修改版本")
    private Integer updateVersion;


    /**
     * 创建人
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "创建人名称")
    private String creator;
    /**
     * 是否发布标志
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否发布标志")
    private Boolean publishFlag;

    /**
     * 发布状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "发布状态")
    private Integer publishStatus;

    public Boolean getPublishFlag() {
        return Objects.equals(publishStatus, VersionStatusEnum.PUBLISHED.getValue());
    }
}
