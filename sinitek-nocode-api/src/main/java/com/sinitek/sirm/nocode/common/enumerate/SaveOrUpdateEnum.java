package com.sinitek.sirm.nocode.common.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0702
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum SaveOrUpdateEnum implements BaseIntegerEnum {
    SAVE(0, "保存"),
    UPDATE(1, "更新");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SaveOrUpdateEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static SaveOrUpdateEnum fromValue(Integer value) {
        return Arrays.stream(SaveOrUpdateEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}